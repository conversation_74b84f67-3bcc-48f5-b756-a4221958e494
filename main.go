package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"nova-proxy/internal/config"
	"nova-proxy/internal/handlers"
	"nova-proxy/internal/middleware"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize configuration loader
	configLoader := config.NewLoader("config.json")

	// Load complete application configuration
	appConfig, err := configLoader.LoadAppConfig()
	if err != nil {
		log.Fatalf("Failed to load app config: %v", err)
	}

	// Initialize services
	balancer := services.NewBalancer()
	proxyService := services.NewProxyService()
	debugService := services.NewDebugService()

	// Initialize logging service
	var loggerService *services.LoggerService
	if appConfig.Logging != nil {
		loggerService = services.NewLoggerService(appConfig.Logging)
		log.Printf("Logging service initialized: enabled=%v, directory=%s",
			appConfig.Logging.Enabled, appConfig.Logging.Directory)
	}

	// Load service configurations into balancer
	for serviceName, serviceConfig := range appConfig.Services {
		balancer.LoadConfig(serviceName, &serviceConfig)
		log.Printf("Loaded config for %s with %d partners", serviceName, len(serviceConfig.Partners))
	}

	// Initialize handlers
	proxyHandler := handlers.NewProxyHandler(balancer, proxyService, debugService)
	adminHandler := handlers.NewAdminHandler(balancer)

	// Setup router
	r := gin.Default()

	// Add logging middleware if logging is enabled
	if loggerService != nil {
		r.Use(middleware.LoggingMiddleware(loggerService))
	}

	// Service routes
	r.POST("/:service", proxyHandler.HandleProxy) // /ocr, /liveness, /facematch

	// Admin routes
	adminGroup := r.Group("/admin")
	{
		adminGroup.POST("/config", adminHandler.UpdateConfig)
		adminGroup.GET("/config", adminHandler.GetConfig)
		adminGroup.GET("/status", adminHandler.GetServiceStatus)

		// Add logging stats endpoint if logging is enabled
		if loggerService != nil {
			adminGroup.GET("/logging/stats", func(c *gin.Context) {
				stats := loggerService.GetStats()
				c.JSON(200, gin.H{"logging_stats": stats})
			})
		}
	}

	// Setup graceful shutdown
	if loggerService != nil {
		setupGracefulShutdown(loggerService)
	}

	// Start server
	log.Println("Starting nova-proxy server on :8080")
	r.Run(":8080")
}

// setupGracefulShutdown sets up graceful shutdown for the logging service
func setupGracefulShutdown(loggerService *services.LoggerService) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Shutting down logging service...")
		loggerService.Shutdown()
		log.Println("Logging service shutdown complete")
		os.Exit(0)
	}()
}
