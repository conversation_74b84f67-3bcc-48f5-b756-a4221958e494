package main

import (
	"encoding/json"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

type APIConfig struct {
	Format         string                 `json:"format"`
	Request        map[string]string      `json:"request"`
	RequestHeaders map[string]string      `json:"request_headers"`
	Response       map[string]interface{} `json:"response"`
}

func main() {
	r := gin.Default()

	// Load all API configs from api/ directory
	configs := loadAPIConfigs("api/")

	// Setup routes dynamically
	for endpoint, config := range configs {
		setupRoute(r, endpoint, config)
	}

	r.Run(":8081")
}

func loadAPIConfigs(dir string) map[string]*APIConfig {
	configs := make(map[string]*APIConfig)

	files, _ := filepath.Glob(filepath.Join(dir, "*.json"))
	for _, file := range files {
		data, _ := os.ReadFile(file)

		var config APIConfig
		json.Unmarshal(data, &config)

		endpoint := strings.TrimSuffix(filepath.Base(file), ".json")
		configs[endpoint] = &config
	}

	return configs
}

func setupRoute(r *gin.Engine, endpoint string, config *APIConfig) {
	r.POST("/"+endpoint, func(c *gin.Context) {
		c.JSON(http.StatusOK, config.Response)
	})
}
