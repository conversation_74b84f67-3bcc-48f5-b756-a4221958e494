package services

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"io"
	"mime/multipart"

	"nova-proxy/internal/models"
	"nova-proxy/internal/utils"
)

// ProxyService handles proxy-related operations
type ProxyService struct{}

// NewProxyService creates a new proxy service instance
func NewProxyService() *ProxyService {
	return &ProxyService{}
}

// BuildJSONFromReq converts mapped data to JSON format with base64 encoding for files
func (p *ProxyService) BuildJSONFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})

	// Copy mapped data (stream convert files to base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			base64Data, err := p.streamFileToBase64(fh)
			if err != nil {
				return nil, err
			}
			jsonData[key] = base64Data
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}

	// Add hardcoded fields
	for key, val := range hardcoded {
		jsonData[key] = val
	}

	return jsonData, nil
}

// streamFileToBase64 efficiently converts a file to base64 using streaming
func (p *ProxyService) streamFileToBase64(fh *multipart.FileHeader) (string, error) {
	f, err := fh.Open()
	if err != nil {
		return "", err
	}
	defer f.Close()

	// For small files (< 1MB), use buffer pool
	if fh.Size < 1024*1024 {
		reader := bufio.NewReader(f)
		b := utils.BufferPool32K.Get()
		defer utils.BufferPool32K.Put(b)

		encoder := base64.NewEncoder(base64.StdEncoding, b)
		if _, err := io.Copy(encoder, reader); err != nil {
			return "", err
		}
		encoder.Close()
		return b.String(), nil
	}

	// For larger files, use streaming with pooled buffer to reduce memory usage
	b := utils.BufferPool32K.Get()
	defer utils.BufferPool32K.Put(b)

	encoder := base64.NewEncoder(base64.StdEncoding, b)

	// Use pooled buffer for large files to reduce GC pressure
	buffer := utils.ByteSlicePool32K.Get()
	defer utils.ByteSlicePool32K.Put(buffer)

	for {
		n, err := f.Read(buffer)
		if n > 0 {
			if _, writeErr := encoder.Write(buffer[:n]); writeErr != nil {
				return "", writeErr
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", err
		}
	}

	encoder.Close()
	return b.String(), nil
}

// BuildMultipartFromReq builds multipart form data from mapped data and hardcoded fields
func (p *ProxyService) BuildMultipartFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (io.Reader, string, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	// Add mapped files/text
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			fw, err := w.CreateFormFile(key, fh.Filename)
			if err != nil {
				return nil, "", err
			}
			f, err := fh.Open()
			if err != nil {
				return nil, "", err
			}
			defer f.Close()

			// Use streaming copy with pooled buffer for large files
			if fh.Size > 1024*1024 { // > 1MB
				buffer := utils.ByteSlicePool32K.Get()
				defer utils.ByteSlicePool32K.Put(buffer)
				_, err = io.CopyBuffer(fw, f, buffer)
			} else {
				_, err = io.Copy(fw, f)
			}
			if err != nil {
				return nil, "", err
			}
		} else if str, ok := val.(string); ok && str != "" {
			w.WriteField(key, str)
		}
	}

	// Add hardcoded as text fields
	for key, val := range hardcoded {
		w.WriteField(key, val)
	}

	w.Close()
	return &b, w.FormDataContentType(), nil
}

// PrepareRequestBody prepares the request body based on the upstream format
func (p *ProxyService) PrepareRequestBody(upstream *models.Upstream, mappedData map[string]interface{}) (io.Reader, string, error) {
	if upstream.Format == "json" {
		jsonData, err := p.BuildJSONFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			return nil, "", err
		}
		jsonBytes, _ := json.Marshal(jsonData)
		return bytes.NewReader(jsonBytes), "application/json", nil
	}

	// Default to multipart
	return p.BuildMultipartFromReq(mappedData, upstream.HardcodedFields)
}
