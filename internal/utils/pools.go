package utils

import (
	"bytes"
	"sync"
)

// BufferPool provides a pool of reusable byte buffers to reduce GC pressure
type BufferPool struct {
	pool sync.Pool
}

// NewBufferPool creates a new buffer pool
func NewBufferPool() *BufferPool {
	return &BufferPool{
		pool: sync.Pool{
			New: func() interface{} {
				// Create buffers with reasonable initial capacity
				return bytes.NewBuffer(make([]byte, 0, 32*1024)) // 32KB initial capacity
			},
		},
	}
}

// Get retrieves a buffer from the pool
func (bp *BufferPool) Get() *bytes.Buffer {
	return bp.pool.Get().(*bytes.Buffer)
}

// Put returns a buffer to the pool after resetting it
func (bp *BufferPool) Put(buf *bytes.Buffer) {
	if buf != nil {
		buf.Reset()
		// Only pool buffers that aren't too large to avoid memory bloat
		if buf.Cap() <= 1024*1024 { // 1MB max capacity
			bp.pool.Put(buf)
		}
	}
}

// ByteSlicePool provides a pool of reusable byte slices
type ByteSlicePool struct {
	pool sync.Pool
	size int
}

// NewByteSlicePool creates a new byte slice pool with fixed size slices
func NewByteSlicePool(size int) *ByteSlicePool {
	return &ByteSlicePool{
		size: size,
		pool: sync.Pool{
			New: func() interface{} {
				return make([]byte, size)
			},
		},
	}
}

// Get retrieves a byte slice from the pool
func (bsp *ByteSlicePool) Get() []byte {
	return bsp.pool.Get().([]byte)
}

// Put returns a byte slice to the pool
func (bsp *ByteSlicePool) Put(slice []byte) {
	if slice != nil && len(slice) == bsp.size {
		bsp.pool.Put(slice)
	}
}

// Global pools for common use cases
var (
	// BufferPool32K for 32KB buffers (good for file processing)
	BufferPool32K = NewBufferPool()
	
	// ByteSlicePool32K for 32KB byte slices (good for streaming)
	ByteSlicePool32K = NewByteSlicePool(32 * 1024)
	
	// ByteSlicePool4K for 4KB byte slices (good for small operations)
	ByteSlicePool4K = NewByteSlicePool(4 * 1024)
)
