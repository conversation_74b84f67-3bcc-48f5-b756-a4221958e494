package models

import (
	"encoding/json"
	"time"
)

// Duration is a custom type that can unmarshal from both string and numeric JSON values
type Duration time.Duration

// UnmarshalJSON implements json.Unmarshaler for Duration
func (d *Duration) UnmarshalJSON(data []byte) error {
	// Try to unmarshal as string first
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		duration, err := time.ParseDuration(str)
		if err != nil {
			return err
		}
		*d = Duration(duration)
		return nil
	}

	// Try to unmarshal as numeric value (nanoseconds)
	var ns int64
	if err := json.Unmarshal(data, &ns); err != nil {
		return err
	}
	*d = Duration(time.Duration(ns))
	return nil
}

// MarshalJSON implements json.Marshaler for Duration
func (d Duration) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Duration(d).String())
}

// String returns the string representation of the duration
func (d Duration) String() string {
	return time.Duration(d).String()
}

// ToDuration converts to time.Duration
func (d Duration) ToDuration() time.Duration {
	return time.Duration(d)
}

// LogLevel represents different logging levels
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// LogConfig represents logging configuration
type LogConfig struct {
	Enabled         bool     `json:"enabled"`
	Level           LogLevel `json:"level"`
	Directory       string   `json:"directory"`
	MaxFileSize     int64    `json:"max_file_size"`     // in bytes
	MaxFiles        int      `json:"max_files"`         // number of files to keep
	FlushInterval   Duration `json:"flush_interval"`    // how often to flush logs to disk
	BufferSize      int      `json:"buffer_size"`       // channel buffer size
	IncludeHeaders  bool     `json:"include_headers"`   // whether to log request/response headers
	IncludeBody     bool     `json:"include_body"`      // whether to log request/response body
	MaxBodySample   int      `json:"max_body_sample"`   // max bytes of body to log
	CompressOldLogs bool     `json:"compress_old_logs"` // whether to compress rotated logs
	LogRequests     bool     `json:"log_requests"`      // whether to log incoming requests
	LogResponses    bool     `json:"log_responses"`     // whether to log outgoing responses
	LogProxyEvents  bool     `json:"log_proxy_events"`  // whether to log proxy events
}

// RequestLogEntry represents a logged HTTP request
type RequestLogEntry struct {
	ID         string            `json:"id"`
	Timestamp  time.Time         `json:"timestamp"`
	Method     string            `json:"method"`
	URL        string            `json:"url"`
	Headers    map[string]string `json:"headers,omitempty"`
	Body       string            `json:"body,omitempty"`
	BodySize   int64             `json:"body_size"`
	RemoteAddr string            `json:"remote_addr"`
	UserAgent  string            `json:"user_agent,omitempty"`
	Service    string            `json:"service,omitempty"`
	RequestID  string            `json:"request_id,omitempty"`
}

// ResponseLogEntry represents a logged HTTP response
type ResponseLogEntry struct {
	ID          string            `json:"id"`
	RequestID   string            `json:"request_id"`
	Timestamp   time.Time         `json:"timestamp"`
	StatusCode  int               `json:"status_code"`
	Headers     map[string]string `json:"headers,omitempty"`
	Body        string            `json:"body,omitempty"`
	BodySize    int64             `json:"body_size"`
	Duration    time.Duration     `json:"duration"`
	UpstreamURL string            `json:"upstream_url,omitempty"`
	PartnerName string            `json:"partner_name,omitempty"`
	Service     string            `json:"service,omitempty"`
	Error       string            `json:"error,omitempty"`
}

// ProxyLogEntry represents a proxy event log
type ProxyLogEntry struct {
	ID            string                 `json:"id"`
	RequestID     string                 `json:"request_id"`
	Timestamp     time.Time              `json:"timestamp"`
	Event         string                 `json:"event"` // "request_start", "upstream_selected", "response_received", etc.
	Service       string                 `json:"service"`
	PartnerName   string                 `json:"partner_name,omitempty"`
	UpstreamURL   string                 `json:"upstream_url,omitempty"`
	Duration      time.Duration          `json:"duration,omitempty"`
	Error         string                 `json:"error,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	DebugMode     bool                   `json:"debug_mode"`
	RoutingReason string                 `json:"routing_reason,omitempty"`
}
