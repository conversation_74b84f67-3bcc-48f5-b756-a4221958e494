package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
)

// AppConfig represents the complete application configuration
type AppConfig struct {
	Services map[string]models.ServiceConfig `json:"services,omitempty"`
	Logging  *models.LogConfig               `json:"logging,omitempty"`
}

// Loader handles configuration loading and management
type Loader struct {
	configPath string
}

// NewLoader creates a new configuration loader
func NewLoader(configPath string) *Loader {
	return &Loader{
		configPath: configPath,
	}
}

// LoadConfig loads configuration from file and applies it to the balancer
func (l *Loader) LoadConfig(balancer *services.Balancer) error {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Try to detect the configuration format
	if l.isNewFormat(data) {
		return l.loadNewFormat(data, balancer)
	} else {
		return l.loadLegacyFormat(data, balancer)
	}
}

// LoadAppConfig loads the complete application configuration including logging
func (l *Loader) LoadAppConfig() (*AppConfig, error) {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Check if it's the new app config format with top-level "services" and "logging"
	var testConfig map[string]interface{}
	if err := json.Unmarshal(data, &testConfig); err != nil {
		return nil, fmt.Errorf("failed to parse config: %w", err)
	}

	if _, hasServices := testConfig["services"]; hasServices {
		// New app config format
		var appConfig AppConfig
		if err := json.Unmarshal(data, &appConfig); err != nil {
			return nil, fmt.Errorf("failed to unmarshal app config: %w", err)
		}

		// Use default logging config if not provided
		if appConfig.Logging == nil {
			appConfig.Logging = l.getDefaultLogConfig()
		} else {
			// Parse duration string if needed
			if err := l.parseLogConfigDurations(appConfig.Logging); err != nil {
				return nil, fmt.Errorf("failed to parse logging config: %w", err)
			}
		}

		return &appConfig, nil
	}

	// Try to detect if it's the service-only format
	if l.isServiceOnlyFormat(data) {
		// Service-only format without logging
		var serviceConfigs map[string]models.ServiceConfig
		if err := json.Unmarshal(data, &serviceConfigs); err != nil {
			return nil, fmt.Errorf("failed to unmarshal service config: %w", err)
		}
		return &AppConfig{
			Services: serviceConfigs,
			Logging:  l.getDefaultLogConfig(),
		}, nil
	} else {
		// Legacy format
		var rawConfigs map[string][]models.Upstream
		if err := json.Unmarshal(data, &rawConfigs); err != nil {
			return nil, fmt.Errorf("failed to unmarshal legacy config: %w", err)
		}

		// Convert to new format
		serviceConfigs := make(map[string]models.ServiceConfig)
		for service, upstreams := range rawConfigs {
			partners := l.convertUpstreamsToPartners(upstreams)
			serviceConfigs[service] = models.ServiceConfig{
				Partners: partners,
			}
		}

		return &AppConfig{
			Services: serviceConfigs,
			Logging:  l.getDefaultLogConfig(),
		}, nil
	}
}

// getDefaultLogConfig returns default logging configuration
func (l *Loader) getDefaultLogConfig() *models.LogConfig {
	return &models.LogConfig{
		Enabled:         true,
		Level:           models.LogLevelInfo,
		Directory:       "./logs",
		MaxFileSize:     100 * 1024 * 1024, // 100MB
		MaxFiles:        10,
		FlushInterval:   models.Duration(30 * time.Second),
		BufferSize:      1000,
		IncludeHeaders:  true,
		IncludeBody:     true,
		MaxBodySample:   1000,
		CompressOldLogs: true,
		LogRequests:     true,
		LogResponses:    true,
		LogProxyEvents:  true,
	}
}

// parseLogConfigDurations parses duration strings in logging configuration
func (l *Loader) parseLogConfigDurations(logConfig *models.LogConfig) error {
	// The FlushInterval might be stored as a string in JSON, parse it
	// This is handled automatically by Go's JSON unmarshaling for time.Duration
	// if the JSON field is a string like "30s", "1m", etc.
	return nil
}

// isNewFormat detects if the configuration uses the new partner-based format
func (l *Loader) isNewFormat(data []byte) bool {
	var testConfig map[string]interface{}
	if err := json.Unmarshal(data, &testConfig); err != nil {
		return false
	}

	// Check if there's a "services" key at the top level (new app config format)
	if _, hasServices := testConfig["services"]; hasServices {
		return true
	}

	// Check if any service has a "partners" field (service-only new format)
	for _, serviceConfig := range testConfig {
		if serviceMap, ok := serviceConfig.(map[string]interface{}); ok {
			if _, hasPartners := serviceMap["partners"]; hasPartners {
				return true
			}
		}
	}
	return false
}

// isServiceOnlyFormat detects if the configuration is service-only format (no top-level services key)
func (l *Loader) isServiceOnlyFormat(data []byte) bool {
	var testConfig map[string]interface{}
	if err := json.Unmarshal(data, &testConfig); err != nil {
		return false
	}

	// Check if any service has a "partners" field (service-only new format)
	for _, serviceConfig := range testConfig {
		if serviceMap, ok := serviceConfig.(map[string]interface{}); ok {
			if _, hasPartners := serviceMap["partners"]; hasPartners {
				return true
			}
		}
	}
	return false
}

// loadNewFormat loads the new partner-based configuration format
func (l *Loader) loadNewFormat(data []byte, balancer *services.Balancer) error {
	var configs map[string]models.ServiceConfig
	if err := json.Unmarshal(data, &configs); err != nil {
		return fmt.Errorf("failed to unmarshal new format config: %w", err)
	}

	for service, config := range configs {
		fmt.Printf("Loaded config for %s with %d partners\n", service, len(config.Partners))
		balancer.LoadConfig(service, &config)
	}

	return nil
}

// loadLegacyFormat loads the old upstream-based configuration format
func (l *Loader) loadLegacyFormat(data []byte, balancer *services.Balancer) error {
	var rawConfigs map[string][]models.Upstream
	if err := json.Unmarshal(data, &rawConfigs); err != nil {
		return fmt.Errorf("failed to unmarshal legacy config: %w", err)
	}

	// Convert old format to new Partner-based format and load into balancer
	for service, upstreams := range rawConfigs {
		// Convert old upstream format to partner format for backward compatibility
		partners := l.convertUpstreamsToPartners(upstreams)
		cfg := &models.ServiceConfig{
			Partners: partners,
		}
		fmt.Printf("Loaded legacy config for %s with %d partners (converted from %d upstreams)\n",
			service, len(cfg.Partners), len(upstreams))
		balancer.LoadConfig(service, cfg)
	}

	return nil
}

// LoadConfigFromData loads configuration from raw data
func (l *Loader) LoadConfigFromData(data []byte, balancer *services.Balancer) error {
	// Try to detect the configuration format
	if l.isNewFormat(data) {
		return l.loadNewFormat(data, balancer)
	} else {
		return l.loadLegacyFormat(data, balancer)
	}
}

// convertUpstreamsToPartners converts old upstream format to new partner format for backward compatibility
func (l *Loader) convertUpstreamsToPartners(upstreams []models.Upstream) []models.Partner {
	partners := make([]models.Partner, len(upstreams))

	for i, upstream := range upstreams {
		partners[i] = models.Partner{
			Name:      fmt.Sprintf("legacy-partner-%d", i),
			Upstreams: []models.Upstream{upstream},
		}
	}

	return partners
}

// ValidateConfig validates the configuration structure
func (l *Loader) ValidateConfig(configData map[string]models.ServiceConfig) error {
	for service, config := range configData {
		if len(config.Partners) == 0 {
			return fmt.Errorf("service %s has no partners configured", service)
		}

		for i, partner := range config.Partners {
			if partner.Name == "" {
				return fmt.Errorf("service %s partner %d has empty name", service, i)
			}
			if len(partner.Upstreams) == 0 {
				return fmt.Errorf("service %s partner %s has no upstreams", service, partner.Name)
			}

			for j, upstream := range partner.Upstreams {
				if upstream.URL == "" {
					return fmt.Errorf("service %s partner %s upstream %d has empty URL", service, partner.Name, j)
				}
				if upstream.Weight <= 0 {
					return fmt.Errorf("service %s partner %s upstream %d has invalid weight: %d", service, partner.Name, j, upstream.Weight)
				}
				if upstream.Format != "json" && upstream.Format != "multipart/form-data" {
					return fmt.Errorf("service %s partner %s upstream %d has unsupported format: %s", service, partner.Name, j, upstream.Format)
				}
			}
		}
	}

	return nil
}
