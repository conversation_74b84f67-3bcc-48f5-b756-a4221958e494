package middleware

import (
	"bytes"
	"fmt"
	"io"
	"strings"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// responseWriter wraps gin.ResponseWriter to capture response data
type responseWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

func (rw *responseWriter) Write(b []byte) (int, error) {
	// Write to both the original response and our buffer
	rw.body.Write(b)
	return rw.ResponseWriter.Write(b)
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// LoggingMiddleware creates a Gin middleware for request/response logging
func LoggingMiddleware(logger *services.LoggerService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip logging if not enabled
		if logger == nil {
			c.Next()
			return
		}

		startTime := time.Now()
		requestID := generateRequestID(c)

		// Set request ID in context for use by handlers
		c.Set("request_id", requestID)

		// Capture request data
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			// Restore the body for the handler to use
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// Create response writer wrapper
		rw := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBuffer([]byte{}),
			statusCode:     200, // default status code
		}
		c.Writer = rw

		// Log request entry
		go func() {
			requestEntry := createRequestLogEntry(c, requestID, requestBody, startTime)
			logger.LogRequest(requestEntry)
		}()

		// Process the request
		c.Next()

		// Log response entry
		go func() {
			responseEntry := createResponseLogEntry(c, rw, requestID, startTime)
			logger.LogResponse(responseEntry)
		}()

		// Log proxy event if this is a proxy request
		if service := c.Param("service"); service != "" {
			go func() {
				proxyEntry := createProxyLogEntry(c, requestID, startTime, service)
				logger.LogProxyEvent(proxyEntry)
			}()
		}
	}
}

// generateRequestID creates a unique request ID
func generateRequestID(c *gin.Context) string {
	// Try to get existing request ID from headers
	if reqID := c.GetHeader("X-Request-ID"); reqID != "" {
		return reqID
	}

	// Generate new UUID-based request ID
	service := c.Param("service")
	if service == "" {
		service = "admin"
	}

	return fmt.Sprintf("%s-%s", service, uuid.New().String()[:8])
}

// createRequestLogEntry creates a request log entry from the Gin context
func createRequestLogEntry(c *gin.Context, requestID string, body []byte, startTime time.Time) *models.RequestLogEntry {
	// Extract headers if configured
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0] // Take first value
		}
	}

	// Process body based on content type
	bodyStr := ""
	bodySize := int64(len(body))

	if len(body) > 0 {
		contentType := c.GetHeader("Content-Type")
		
		// Check if it's likely binary content
		if utils.IsBinaryContent(body) {
			bodyStr = fmt.Sprintf("[BINARY_CONTENT:%d bytes]", len(body))
		} else {
			// For text content, truncate if too large
			maxBodySample := 1000 // default max sample size
			if len(body) > maxBodySample {
				bodyStr = string(body[:maxBodySample]) + "... [TRUNCATED]"
			} else {
				bodyStr = string(body)
			}
		}

		// Special handling for multipart forms
		if strings.Contains(contentType, "multipart/form-data") {
			bodyStr = fmt.Sprintf("[MULTIPART_FORM:%d bytes]", len(body))
		}
	}

	return &models.RequestLogEntry{
		ID:         uuid.New().String(),
		Timestamp:  startTime,
		Method:     c.Request.Method,
		URL:        c.Request.URL.String(),
		Headers:    headers,
		Body:       bodyStr,
		BodySize:   bodySize,
		RemoteAddr: c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Service:    c.Param("service"),
		RequestID:  requestID,
	}
}

// createResponseLogEntry creates a response log entry
func createResponseLogEntry(c *gin.Context, rw *responseWriter, requestID string, startTime time.Time) *models.ResponseLogEntry {
	// Extract response headers
	headers := make(map[string]string)
	for key, values := range rw.Header() {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// Process response body
	responseBody := rw.body.Bytes()
	bodyStr := ""
	bodySize := int64(len(responseBody))

	if len(responseBody) > 0 {
		// Check if it's binary content
		if utils.IsBinaryContent(responseBody) {
			bodyStr = fmt.Sprintf("[BINARY_CONTENT:%d bytes]", len(responseBody))
		} else {
			// For text content, truncate if too large
			maxBodySample := 1000 // default max sample size
			if len(responseBody) > maxBodySample {
				bodyStr = string(responseBody[:maxBodySample]) + "... [TRUNCATED]"
			} else {
				bodyStr = string(responseBody)
			}
		}
	}

	// Get error information if any
	errorStr := ""
	if len(c.Errors) > 0 {
		errorStr = c.Errors.String()
	}

	// Get upstream information from context if available
	upstreamURL := ""
	partnerName := ""
	if upstream, exists := c.Get("upstream_url"); exists {
		if url, ok := upstream.(string); ok {
			upstreamURL = url
		}
	}
	if partner, exists := c.Get("partner_name"); exists {
		if name, ok := partner.(string); ok {
			partnerName = name
		}
	}

	return &models.ResponseLogEntry{
		ID:          uuid.New().String(),
		RequestID:   requestID,
		Timestamp:   time.Now(),
		StatusCode:  rw.statusCode,
		Headers:     headers,
		Body:        bodyStr,
		BodySize:    bodySize,
		Duration:    time.Since(startTime),
		UpstreamURL: upstreamURL,
		PartnerName: partnerName,
		Service:     c.Param("service"),
		Error:       errorStr,
	}
}

// createProxyLogEntry creates a proxy event log entry
func createProxyLogEntry(c *gin.Context, requestID string, startTime time.Time, service string) *models.ProxyLogEntry {
	// Get routing information from context
	partnerName := c.GetHeader("x-partner")
	businessCode := c.GetHeader("x-biz")
	
	// Determine routing reason
	routingReason := "weighted_round_robin" // default
	if partnerName != "" {
		routingReason = "partner_header"
	} else if businessCode != "" {
		routingReason = "business_code"
	}

	// Get upstream information from context if available
	upstreamURL := ""
	if upstream, exists := c.Get("upstream_url"); exists {
		if url, ok := upstream.(string); ok {
			upstreamURL = url
		}
	}

	// Get debug mode information
	debugMode := false
	if debug, exists := c.Get("debug_mode"); exists {
		if d, ok := debug.(bool); ok {
			debugMode = d
		}
	}

	// Create metadata
	metadata := map[string]interface{}{
		"method":        c.Request.Method,
		"user_agent":    c.GetHeader("User-Agent"),
		"remote_addr":   c.ClientIP(),
		"content_type":  c.GetHeader("Content-Type"),
	}

	if partnerName != "" {
		metadata["partner_header"] = partnerName
	}
	if businessCode != "" {
		metadata["business_code"] = businessCode
	}

	return &models.ProxyLogEntry{
		ID:            uuid.New().String(),
		RequestID:     requestID,
		Timestamp:     startTime,
		Event:         "request_processed",
		Service:       service,
		PartnerName:   partnerName,
		UpstreamURL:   upstreamURL,
		Duration:      time.Since(startTime),
		Error:         "",
		Metadata:      metadata,
		DebugMode:     debugMode,
		RoutingReason: routingReason,
	}
}
